import { PrismaClient, Role, Gender, InsurerCompany, PolicyStatus, AuctionState } from '@prisma/client';
import { createClient } from '@supabase/supabase-js';

const prisma = new PrismaClient();

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function main() {
  console.log('Starting database seeding...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ACCOUNT_HOLDER,
      phone: '+***********',
      profile: {
        firstName: 'María',
        lastName: '<PERSON>',
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        registrationClass: 'Clase A',
        registrationKey: 'REG_001_2024',
        registrationDate: new Date('2024-01-15'),
        legalName: '<PERSON><PERSON><PERSON>',
        identifier: 'B12345678',
        insurerCompany: InsurerCompany.MAPFRE,
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: 'Ana',
        lastName: 'Rodríguez',
        registrationClass: 'Clase A',
        registrationKey: 'REG_002_2024',
        registrationDate: new Date('2024-02-20'),
        legalName: null,
        identifier: 'B87654321',
        insurerCompany: InsurerCompany.ALLIANZ,
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+34611223344',
      profile: {
        firstName: 'Laura',
        lastName: 'Gómez',
        registrationClass: 'Clase B',
        registrationKey: 'REG_003_2024',
        registrationDate: new Date('2024-03-10'),
        legalName: null,
        identifier: 'C98765432',
        insurerCompany: InsurerCompany.AXA,
        isAuthorizedByOther: true,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ADMIN,
      phone: '+34911234567',
      profile: {
        firstName: 'Admin',
        lastName: 'User',
      },
    },
  ];

  // To ensure a clean slate, first delete all existing users from Supabase Auth and the public.User table
  await prisma.user.deleteMany({});
  console.log('Deleted all existing users from the public.User table.');
  
  const { data: { users: allUsers }, error: listError } = await supabase.auth.admin.listUsers();

  if (listError) {
    console.error('Error fetching users to delete:', listError);
  } else {
    for (const user of allUsers) {
      const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);
      if (deleteError) {
        console.error(`Error deleting user ${user.email}:`, deleteError);
      } else {
        console.log(`Deleted existing user ${user.email} from Supabase Auth.`);
      }
    }
  }

  for (const userData of testUsers) {
    // Create user in Supabase Auth
    const { data: newAuthUser, error: newAuthError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      phone: userData.phone,
      email_confirm: true, // Auto-confirm user
      phone_confirm: true, // Auto-confirm phone
      user_metadata: {
        display_name: `${userData.profile.firstName} ${userData.profile.lastName}`,
        role: userData.role,
        first_name: userData.profile.firstName,
        last_name: userData.profile.lastName,
        phone: userData.phone,
      },
    });

    if (newAuthError) {
      console.error(`Error creating user ${userData.email} in Supabase Auth:`, newAuthError);
      continue;
    }
    const userId = newAuthUser.user.id;
    console.log(`Created user ${userData.email} in Supabase Auth with ID: ${userId}`);

    // Wait a moment for the trigger to process the user creation
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if the user was created by the trigger
    const dbUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (dbUser) {
      console.log(`User ${userData.email} was created in the database by the trigger.`);
    } else {
      console.log(`Trigger didn't create user ${userData.email}, creating manually...`);
      // Fallback: create manually if trigger didn't work
      const manualUser = await prisma.user.create({
        data: {
          id: userId,
          email: userData.email,
          phone: userData.phone,
          firstName: userData.profile.firstName,
          lastName: userData.profile.lastName,
          displayName: `${userData.profile.firstName} ${userData.profile.lastName}`,
          role: userData.role,
        },
      });
      console.log(`Manually created user ${manualUser.email} in the database.`);
    }

    // Check if profiles were created by the trigger, create manually if not
    switch (userData.role) {
      case Role.ACCOUNT_HOLDER:
        const accountHolderProfile = await prisma.accountHolderProfile.findUnique({
          where: { userId },
        });
        if (accountHolderProfile) {
          console.log(`Account holder profile for ${userData.email} was created by the trigger.`);
        } else {
          console.log(`Creating account holder profile manually for ${userData.email}...`);
          await prisma.accountHolderProfile.create({
            data: { userId },
          });
          console.log(`Manually created account holder profile for ${userData.email}`);
        }
        break;

      case Role.BROKER:
        const brokerProfile = await prisma.brokerProfile.findUnique({
          where: { userId },
        });
        if (brokerProfile) {
          console.log(`Broker profile for ${userData.email} was created by the trigger.`);
          // Update with real data if it was created with defaults
          const profileData = userData.profile as any;
          await prisma.brokerProfile.update({
            where: { userId },
            data: {
              registrationClass: profileData.registrationClass,
              registrationKey: profileData.registrationKey,
              registrationDate: profileData.registrationDate,
              legalName: profileData.legalName,
              identifier: profileData.identifier,
              insurerCompany: profileData.insurerCompany,
              isAuthorizedByOther: profileData.isAuthorizedByOther,
              isComplementary: profileData.isComplementary,
              isGroupAgent: profileData.isGroupAgent,
            },
          });
          console.log(`Updated broker profile with real data for ${userData.email}`);
        } else {
          console.log(`Creating broker profile manually for ${userData.email}...`);
          const profileData = userData.profile as any;
          await prisma.brokerProfile.create({
            data: {
              userId,
              registrationClass: profileData.registrationClass,
              registrationKey: profileData.registrationKey,
              registrationDate: profileData.registrationDate,
              legalName: profileData.legalName,
              identifier: profileData.identifier,
              insurerCompany: profileData.insurerCompany,
              isAuthorizedByOther: profileData.isAuthorizedByOther,
              isComplementary: profileData.isComplementary,
              isGroupAgent: profileData.isGroupAgent,
            },
          });
          console.log(`Manually created broker profile for ${userData.email}`);
        }
        break;

      case Role.ADMIN:
        const adminProfile = await prisma.adminProfile.findUnique({
          where: { userId },
        });
        if (adminProfile) {
          console.log(`Admin profile for ${userData.email} was created by the trigger.`);
        } else {
          console.log(`Creating admin profile manually for ${userData.email}...`);
          await prisma.adminProfile.create({
            data: { userId },
          });
          console.log(`Manually created admin profile for ${userData.email}`);
        }
        break;
    }
  }

  // ============================================================================
  // CREATE COMPREHENSIVE TEST DATA
  // ============================================================================

  console.log('\n🏗️  Creating comprehensive test data...');

  // Use current date for "today" to ensure auctions are always in the future
  const today = new Date();
  today.setHours(10, 0, 0, 0); // Set to 10:00 AM for consistency
  console.log(`🗓️  Using current date for "today": ${today.toISOString()}`);

  // Get the created users for relationships
  const accountHolderUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { accountHolderProfile: true }
  });

  const broker1User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker2User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker3User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  if (!accountHolderUser?.accountHolderProfile || !broker1User?.brokerProfile || !broker2User?.brokerProfile || !broker3User?.brokerProfile) {
    throw new Error('Required user profiles not found');
  }

  // Create addresses for brokers
  console.log('📍 Creating broker addresses...');
  await prisma.address.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      street: 'Calle Gran Vía, 45, 3º A',
      city: 'Madrid',
      province: 'MADRID',
      region: 'MADRID',
      country: 'SPAIN',
      postalCode: '28013'
    }
  });

  await prisma.address.create({
    data: {
      brokerId: broker2User.brokerProfile.id,
      street: 'Avenida Diagonal, 123, 2º B',
      city: 'Barcelona',
      province: 'BARCELONA',
      region: 'CATALONIA',
      country: 'SPAIN',
      postalCode: '08028'
    }
  });

  await prisma.address.create({
    data: {
      brokerId: broker3User.brokerProfile.id,
      street: 'Plaza del Ayuntamiento, 1',
      city: 'Valencia',
      province: 'VALENCIA',
      region: 'VALENCIAN_COMMUNITY',
      country: 'SPAIN',
      postalCode: '46002'
    }
  });

  console.log(`✅ Created addresses for ${broker1User.firstName}, ${broker2User.firstName} and ${broker3User.firstName}`);

  // Create assets for the account holder
  console.log('🚗 Creating assets...');
  const carAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'CAR',
      description: 'Seat León 1.5 TSI FR',
      value: 28500.00,
      vehicleDetails: {
        create: {
          brand: 'Seat',
          model: 'León',
          year: 2022,
          firstRegistrationDate: new Date('2022-03-15'),
          licensePlate: '1234ABC',
          version: '1.5 TSI FR',
          fuelType: 'GASOLINE',
          powerCv: 150,
          chassisNumber: 'VSSZZZKJZHXXXXXX',
          isLeased: false,
          seats: 5,
          garageType: 'PRIVATE',
          usageType: 'PRIVATE_REGULAR',
          kmPerYear: 'FROM_10000_TO_12000'
        }
      }
    }
  });

  const motorcycleAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'MOTORCYCLE',
      description: 'Yamaha MT-07',
      value: 8500.00,
      vehicleDetails: {
        create: {
          brand: 'Yamaha',
          model: 'MT-07',
          year: 2023,
          firstRegistrationDate: new Date('2023-05-20'),
          licensePlate: '5678DEF',
          version: 'ABS',
          fuelType: 'GASOLINE',
          powerCv: 74.8,
          chassisNumber: 'JYARM321000XXXXXX',
          isLeased: false,
          seats: 2,
          garageType: 'SHARED_GUARDED',
          usageType: 'PRIVATE_OCCASIONAL',
          kmPerYear: 'FROM_4000_TO_6000'
        }
      }
    }
  });

  console.log(`✅ Created car asset: ${carAsset.description} and motorcycle: ${motorcycleAsset.description}`);

  // Create insured parties
  console.log('👥 Creating insured parties...');
  const mainInsuredParty = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'María',
      lastName: 'García López',
      displayName: 'María García López',
      identification: '12345678A',
      roles: ['POLICYHOLDER','MAIN_DRIVER','OWNER'],
      gender: Gender.FEMALE,
      birthDate: new Date('1985-03-15'),
      driverLicenseNumber: '12345678A',
      driverLicenseIssuedAt: new Date('2003-04-01'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  const additionalDriver = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'Juan',
      lastName: 'García Martín',
      displayName: 'Juan García Martín',
      identification: '87654321B',
      roles: ['ADDITIONAL_DRIVER'],
      gender: Gender.MALE,
      birthDate: new Date('1982-07-22'),
      driverLicenseNumber: '87654321B',
      driverLicenseIssuedAt: new Date('2000-10-15'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  console.log(`✅ Created insured parties: ${mainInsuredParty.firstName} ${mainInsuredParty.lastName} and ${additionalDriver.firstName} ${additionalDriver.lastName}`);

  // Create some documentation records
  console.log('📄 Creating documentation...');
  const carPolicyDocument = await prisma.documentation.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      type: 'POLICY_DOCUMENT',
      url: '/documents/policies/poliza_coche_2024.pdf',
      fileName: 'poliza_coche_2024.pdf',
      fileSize: 2048576, // 2MB
      mimeType: 'application/pdf'
    }
  });

  const motorcyclePolicyDocument = await prisma.documentation.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      type: 'POLICY_DOCUMENT',
      url: '/documents/policies/poliza_moto_2024.pdf',
      fileName: 'poliza_moto_2024.pdf',
      fileSize: 1536123, // 1.5MB
      mimeType: 'application/pdf'
    }
  });

  const broker1QuoteDocument = await prisma.documentation.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      type: 'QUOTE_DOCUMENT',
      url: '/documents/quotes/cotizacion_moto_broker1.pdf',
      fileName: 'cotizacion_moto_broker1.pdf',
      fileSize: 1024768, // 1MB
      mimeType: 'application/pdf'
    }
  });

  const broker2QuoteDocument = await prisma.documentation.create({
    data: {
      brokerId: broker2User.brokerProfile.id,
      type: 'QUOTE_DOCUMENT',
      url: '/documents/quotes/cotizacion_moto_broker2.pdf',
      fileName: 'cotizacion_moto_broker2.pdf',
      fileSize: 1124768, // 1.1MB
      mimeType: 'application/pdf'
    }
  });

  const broker3QuoteDocument = await prisma.documentation.create({
    data: {
      brokerId: broker3User.brokerProfile.id,
      type: 'QUOTE_DOCUMENT',
      url: '/documents/quotes/cotizacion_moto_broker3.pdf',
      fileName: 'cotizacion_moto_broker3.pdf',
      fileSize: 924768, // 0.9MB
      mimeType: 'application/pdf'
    }
  });

  console.log('✅ Created documentation records');

  // --- Policy & Auction Creation ---
  console.log('🔄 Creating policies and auctions based on scenarios...');

  const policyScenarios = [
    // Scenario 1: Active policy, open auction with bids
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 120, // Expires in 120 days (4 months)
      auctionStatus: AuctionState.OPEN,
      auctionDates: { startOffset: 0, endOffset: 90 }, // Starts today, ends in 90 days (3 months)
      withBids: true,
      asset: carAsset,
      document: carPolicyDocument,
      insuredParties: [mainInsuredParty, additionalDriver],
      insurer: InsurerCompany.MAPFRE,
      policyNumber: 'POL-CAR-2024-001',
    },
    // Scenario 2: Policy renewing soon, open auction without bids
    {
      policyStatus: PolicyStatus.RENEW_SOON,
      policyEndDateOffset: 105, // Expires in 105 days
      auctionStatus: AuctionState.OPEN,
      auctionDates: { startOffset: 0, endOffset: 75 }, // Starts today, ends in 75 days (2.5 months)
      withBids: false,
      asset: motorcycleAsset,
      document: motorcyclePolicyDocument,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.MUTUA_MADRILENA,
      policyNumber: 'POL-MOTO-2024-002',
    },
    // Scenario 3: Policy renewing soon, but auction is already closed
    {
      policyStatus: PolicyStatus.RENEW_SOON,
      policyEndDateOffset: 90, // Expires in 90 days
      auctionStatus: AuctionState.CLOSED,
      auctionDates: { startOffset: -10, endOffset: -3 }, // In the past
      withBids: true,
      asset: null, // Create new asset
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.ALLIANZ,
      policyNumber: 'POL-CAR-2024-003',
    },
    // Scenario 4: Active policy, auction resulted in a signed policy
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 150, // Expires in 150 days (5 months)
      auctionStatus: AuctionState.SIGNED_POLICY,
      auctionDates: { startOffset: -20, endOffset: -13 }, // In the past
      withBids: true,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.AXA,
      policyNumber: 'POL-MOTO-2024-004',
    },
    // Scenario 5: Active policy, auction was canceled
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 130, // Expires in 130 days
      auctionStatus: AuctionState.CANCELED,
      auctionDates: { startOffset: -15, endOffset: -8 }, // In the past
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.GENERALI,
      policyNumber: 'POL-CAR-2024-005',
    },
    // Scenario 6: Expired policy, expired auction
    {
      policyStatus: PolicyStatus.EXPIRED,
      policyEndDateOffset: -5, // Expired 5 days ago
      auctionStatus: AuctionState.EXPIRED,
      auctionDates: { startOffset: -40, endOffset: -33 }, // In the past
      withBids: true,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.ZURICH,
      policyNumber: 'POL-MOTO-2024-006',
    },
    // Scenario 7: Draft policy, no auction
    {
      policyStatus: PolicyStatus.DRAFT,
      policyEndDateOffset: null,
      auctionStatus: null,
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.SANTALUCIA,
      policyNumber: 'POL-DRAFT-2024-007',
    },
    // Scenario 8: Rejected policy, no auction
    {
      policyStatus: PolicyStatus.REJECTED,
      policyEndDateOffset: null,
      auctionStatus: null,
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.DIRECT_SEGUROS,
      policyNumber: 'POL-REJECTED-2024-008',
    },
  ];

  let policyCounter = 1;

  for (const scenario of policyScenarios) {
    console.log(`  - Creating policy #${policyCounter} with status: ${scenario.policyStatus}`);

    let asset = scenario.asset;
    if (!asset) {
      const assetType = policyCounter % 2 === 0 ? 'MOTORCYCLE' : 'CAR';
      const brand = assetType === 'CAR' ? 'Audi' : 'Ducati';
      const model = assetType === 'CAR' ? 'A3' : 'Panigale';
      asset = await prisma.asset.create({
        data: {
          accountHolderId: accountHolderUser.accountHolderProfile.id,
          assetType,
          description: `${brand} ${model} (Policy #${policyCounter})`,
          value: 35000.00,
          vehicleDetails: {
            create: {
              brand,
              model,
              year: 2023,
              firstRegistrationDate: new Date('2023-01-01'),
              licensePlate: `AU${policyCounter.toString().padStart(2, '0')}${scenario.policyStatus.substring(0, 2)}`,
              version: 'Sportback',
              fuelType: 'GASOLINE',
              powerCv: 150,
              chassisNumber: `VSSZZZAUZHXXXX${policyCounter.toString().padStart(2, '0')}`,
              isLeased: false,
              seats: 5,
              garageType: 'PRIVATE',
              usageType: 'PRIVATE_REGULAR',
              kmPerYear: 'FROM_10000_TO_12000'
            }
          }
        }
      });
    }

    let document = scenario.document;
    if (!document) {
        document = await prisma.documentation.create({
            data: {
                accountHolderId: accountHolderUser.accountHolderProfile.id,
                type: 'POLICY_DOCUMENT',
                url: `/documents/policies/poliza_scenario_${policyCounter}.pdf`,
                fileName: `poliza_scenario_${policyCounter}.pdf`,
                fileSize: 1000000,
                mimeType: 'application/pdf',
            },
        });
    }

    const policyData: any = {
        documentId: document.id,
        accountHolderId: accountHolderUser.accountHolderProfile.id,
        assetId: asset.id,
        policyNumber: scenario.policyNumber,
        insurerCompany: scenario.insurer,
        status: scenario.policyStatus,
        isAssetsTypeConfirmed: true,
        paymentPeriod: 'ANNUAL',
        premium: 600.00 + policyCounter * 10,
        productName: `Seguro ${asset.assetType === 'CAR' ? 'Coche' : 'Moto'} #${policyCounter}`,
        termsAccepted: true,
        termsAcceptedAt: new Date('2024-01-01T10:00:00Z'),
        insuredParties: {
            create: scenario.insuredParties.map(p => ({ insuredPartyId: p.id })),
        },
    };

    if (scenario.policyEndDateOffset !== null) {
        const policyEndDate = new Date(today);
        policyEndDate.setDate(today.getDate() + scenario.policyEndDateOffset);
        const policyStartDate = new Date(policyEndDate);
        policyStartDate.setFullYear(policyEndDate.getFullYear() - 1);
        policyData.startDate = policyStartDate;
        policyData.endDate = policyEndDate;
    }

    const newPolicy = await prisma.policy.create({ data: policyData });
    console.log(`    ✅ Created policy ${newPolicy.policyNumber}`);

    // Create sample coverages for the policy
    console.log(`    📋 Adding coverages to policy ${newPolicy.policyNumber}...`);
    const sampleCoverages = [
      {
        type: 'MANDATORY_LIABILITY',
        customName: 'Responsabilidad Civil Obligatoria',
        limit: 70000000.00,
        deductible: null,
        description: 'Cobertura obligatoria de responsabilidad civil'
      },
      {
        type: 'VEHICLE_DAMAGE',
        customName: 'Daños Propios',
        limit: asset.value,
        deductible: 300.00,
        description: 'Cobertura de daños al vehículo propio'
      },
      {
        type: 'THEFT',
        customName: 'Robo',
        limit: asset.value,
        deductible: 150.00,
        description: 'Cobertura contra robo del vehículo'
      },
      {
        type: 'FIRE',
        customName: 'Incendio',
        limit: asset.value,
        deductible: null,
        description: 'Cobertura contra incendio'
      },
      {
        type: 'GLASS_BREAKAGE',
        customName: 'Lunas',
        limit: 500.00,
        deductible: null,
        description: 'Cobertura de cristales'
      },
      {
        type: 'LEGAL_DEFENSE',
        customName: 'Defensa Jurídica',
        limit: 6000.00,
        deductible: null,
        description: 'Asistencia jurídica'
      }
    ];

    for (const coverage of sampleCoverages) {
      await prisma.coverage.create({
        data: {
          policyId: newPolicy.id,
          type: coverage.type as any,
          customName: coverage.customName,
          limit: coverage.limit,
          deductible: coverage.deductible,
          description: coverage.description
        }
      });
    }
    console.log(`    ✅ Added ${sampleCoverages.length} coverages to policy ${newPolicy.policyNumber}`);

    if (scenario.auctionStatus) {
        console.log(`    - Creating auction with status: ${scenario.auctionStatus}`);
        const auctionStartDate = new Date(today);
        if (scenario.auctionDates.startOffset !== undefined) {
          auctionStartDate.setDate(today.getDate() + scenario.auctionDates.startOffset);
        }
        const auctionEndDate = new Date(today);
        if (scenario.auctionDates.endOffset !== undefined) {
          auctionEndDate.setDate(today.getDate() + scenario.auctionDates.endOffset);
        }
        const workingHoursClosedAt = new Date(auctionEndDate);
        workingHoursClosedAt.setDate(workingHoursClosedAt.getDate() - 2);

        const newAuction = await prisma.auction.create({
            data: {
                accountHolderId: accountHolderUser.accountHolderProfile.id,
                policyId: newPolicy.id,
                status: scenario.auctionStatus,
                startDate: auctionStartDate,
                endDate: auctionEndDate,
                workingHoursClosedAt,
                maxWinners: 3,
                minWinners: 1,
            },
        });
        console.log(`    ✅ Created auction ${newAuction.id} for policy ${newPolicy.policyNumber}`);

        await prisma.documentation.update({
            where: { id: document.id },
            data: { relatedAuctionId: newAuction.id },
        });

        if (scenario.withBids) {
            console.log(`    💰 Adding bids to auction ${newAuction.id}...`);
            const brokers = [broker1User, broker2User, broker3User];
            for (let i = 0; i < brokers.length; i++) {
                const broker = brokers[i];
                if (!broker?.brokerProfile) continue;
                const bidDate = new Date(newAuction.startDate);
                bidDate.setHours(bidDate.getHours() + (i + 1) * 2);
                await prisma.bid.create({
                    data: {
                        auctionId: newAuction.id,
                        brokerId: broker.brokerProfile.id,
                        amount: (newPolicy.premium?.toNumber() ?? 600) * (0.9 - i * 0.05), // 10%, 15%, 20% discount
                        createdAt: bidDate,
                    },
                });
            }
            console.log(`    ✅ Added 3 bids to auction ${newAuction.id}`);
        }
    }
    policyCounter++;
  }

  // Add 15 additional bids specifically for POL-CAR-2024-001
  console.log('💰 Adding 15 additional bids to POL-CAR-2024-001...');
  const pol001Auction = await prisma.auction.findFirst({
    where: {
      policy: {
        policyNumber: 'POL-CAR-2024-001'
      }
    },
    include: {
      policy: true
    }
  });

  if (pol001Auction) {
    // Create additional brokers for more bids
    const additionalBrokers = [];
    const brokerCompanies = [
      'MAPFRE', 'ALLIANZ', 'AXA', 'GENERALI', 'ZURICH', 'SANTALUCIA', 'DIRECT_SEGUROS',
      'MUTUA_MADRILENA', 'PELAYO', 'CATALANA_OCCIDENTE', 'LIBERTY_SEGUROS', 'REALE_SEGUROS', 'CASER',
      'FIATC', 'HELVETIA'
    ];
    
    const brokerFirstNames = [
      'Carlos', 'María', 'José', 'Ana', 'Francisco', 'Carmen', 'Antonio', 'Isabel',
      'Manuel', 'Pilar', 'David', 'Rosa', 'Miguel', 'Elena', 'Rafael'
    ];
    
    const brokerLastNames = [
      'García López', 'Martínez Ruiz', 'González Pérez', 'Rodríguez Sánchez', 'López Martín',
      'Hernández Gil', 'Pérez Moreno', 'Sánchez Jiménez', 'Ruiz Álvarez', 'Jiménez Romero',
      'Moreno Castro', 'Muñoz Ortega', 'Álvarez Rubio', 'Romero Iglesias', 'Gutierrez Vargas'
    ];
    
    const brokerCompanyNames = [
       'Seguros Premium', 'Broker Elite', 'Aseguradoras Top', 'Seguros Express',
       'Broker Master', 'Seguros Plus', 'Broker Pro', 'Seguros Direct',
       'Broker Smart', 'Seguros Fast', 'Broker Expert', 'Seguros Quality',
       'Broker Select', 'Seguros Prime', 'Broker Advanced'
     ];

    for (let i = 0; i < 15; i++) {
      const brokerEmail = `broker.extra${i + 4}@zeeguros.com`;
      const brokerPassword = 'password123';
      
      // Create user in Supabase Auth
      const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: brokerEmail,
        password: brokerPassword,
        email_confirm: true,
        user_metadata: {
          role: 'BROKER',
          firstName: brokerFirstNames[i],
          lastName: brokerLastNames[i]
        }
      });

      if (authError) {
        console.error(`Failed to create auth user for ${brokerEmail}:`, authError);
        continue;
      }

      // Create user in database
       const brokerUser = await prisma.user.create({
         data: {
           id: authUser.user.id,
           email: brokerEmail,
           firstName: brokerFirstNames[i],
           lastName: brokerLastNames[i],
           displayName: `${brokerFirstNames[i]} ${brokerLastNames[i]}`,
           role: 'BROKER',
           brokerProfile: {
             create: {
               registrationClass: 'BROKER',
               registrationKey: `REG-EXTRA-${(i + 4).toString().padStart(3, '0')}`,
               registrationDate: new Date(),
               legalName: brokerCompanyNames[i],
               identifier: `ID-EXTRA-${(i + 4).toString().padStart(3, '0')}`,
               insurerCompany: brokerCompanies[i] as InsurerCompany,
               isComplementary: false,
               isGroupAgent: false,
               kycStatus: 'VERIFIED',
               billingAddress: {
                 create: {
                   street: `Calle Broker Extra ${i + 4}`,
                   city: 'Madrid',
                   province: 'MADRID',
                   region: 'MADRID',
                   postalCode: `280${(i % 10).toString().padStart(2, '0')}`
                 }
               }
             }
           }
         },
         include: {
           brokerProfile: true
         }
       });
      
      additionalBrokers.push(brokerUser);
    }

    // Create 15 additional bids with varying amounts and timestamps
    const basePremium = pol001Auction.policy.premium?.toNumber() ?? 600;
    const auctionStart = new Date(pol001Auction.startDate);
    
    for (let i = 0; i < 15; i++) {
      const broker = additionalBrokers[i];
      if (!broker?.brokerProfile) continue;
      
      // Create varied bid amounts (discounts from 5% to 35%)
      const discountPercent = 0.05 + (i * 0.02); // 5%, 7%, 9%, ... up to 35%
      const bidAmount = basePremium * (1 - discountPercent);
      
      // Create varied bid timestamps throughout the auction period
      const bidDate = new Date(auctionStart);
      bidDate.setHours(auctionStart.getHours() + 6 + (i * 3)); // Spread bids over time
      bidDate.setMinutes(Math.floor(Math.random() * 60)); // Random minutes
      
      await prisma.bid.create({
        data: {
          auctionId: pol001Auction.id,
          brokerId: broker.brokerProfile.id,
          amount: Math.round(bidAmount * 100) / 100, // Round to 2 decimal places
          createdAt: bidDate,
        },
      });
    }
    
    console.log(`    ✅ Added 15 additional bids to POL-CAR-2024-001 auction`);
  }

  // Create auction winners for the first scenario
  console.log('🏆 Creating auction winners...');
  const firstAuctionWithBids = await prisma.auction.findFirst({
    where: {
      policy: {
        policyNumber: 'POL-CAR-2024-001'
      }
    },
    include: {
      bids: {
        orderBy: {
          amount: 'asc'
        }
      },
      policy: true
    }
  });

  if (firstAuctionWithBids && firstAuctionWithBids.bids.length >= 3) {
    const bestBids = firstAuctionWithBids.bids;
    const winnerBroker1 = bestBids[0];
    const winnerBroker2 = bestBids[1];
    const winnerBroker3 = bestBids[2];

    if (winnerBroker1 && winnerBroker2 && winnerBroker3) {
        const winner1 = await prisma.auctionWinner.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            brokerId: winnerBroker1.brokerId,
            bidId: winnerBroker1.id,
            position: 1
          }
        });
        const winner2 = await prisma.auctionWinner.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            brokerId: winnerBroker2.brokerId,
            bidId: winnerBroker2.id,
            position: 2
          }
        });
        const winner3 = await prisma.auctionWinner.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            brokerId: winnerBroker3.brokerId,
            bidId: winnerBroker3.id,
            position: 3
          }
        });
        console.log(`✅ Created auction winners for auction ${firstAuctionWithBids.id}`);

        // Simulate one winner paying the commission
        console.log('💸 Simulating commission payment...');
        await prisma.auctionCommission.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            winnerId: winner1.id,
            brokerId: winner1.brokerId,
            amount: (firstAuctionWithBids.policy.premium?.toNumber() ?? 0) * 0.1, // 10% commission
            status: 'PAID',
            paidAt: new Date(),
            stripePaymentIntentId: 'pi_3P...' // Fake payment intent
          }
        });

        // Update winner to reflect data reveal
        await prisma.auctionWinner.update({
          where: { id: winner1.id },
          data: { contactDataRevealedAt: new Date() }
        });
        console.log(`✅ Commission paid by winner and contact data revealed.`);
    }
  }


  // Create a subscription for a broker
  console.log('💳 Creating broker subscription...');
  await prisma.subscription.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      stripeSubscriptionId: 'sub_1P...',
      status: 'active',
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    }
  });

  console.log(`✅ Created subscription for ${broker1User.firstName}`);

  console.log('\n🎉 Comprehensive test data creation completed!');
  console.log('📊 Summary of created data:');
  console.log('   - 5 Users (1 account holder, 3 brokers, 1 admin)');
  console.log(`   - ${policyScenarios.length + 2} Assets (cars, motorcycles) with vehicle details`);
  console.log('   - 2 Insured parties with addresses');
  console.log(`   - ${policyScenarios.length} Policies with various statuses`);
  console.log(`   - ${policyScenarios.filter(p => p.auctionStatus).length} Auctions with various statuses`);
  console.log('   - Multiple bids on auctions with bids');
  console.log('   - 3 Broker addresses');
  console.log('   - Multiple documentation records (policies and quotes)');
  console.log('   - 1 Broker subscription');
  console.log('   - Auction winners and commission payments for testing');


  console.log('Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during database seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
